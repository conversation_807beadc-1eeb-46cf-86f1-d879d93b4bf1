D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\CoverletSourceRootsMapping_FluentDesign.WPF.Tests
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\testhost.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\testhost.exe
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.runner.visualstudio.dotnetcore.testadapter.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.runner.reporters.netcoreapp10.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.runner.utility.netcoreapp10.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.WPF.Tests.deps.json
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.WPF.Tests.runtimeconfig.json
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.WPF.Tests.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.WPF.Tests.pdb
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.WPF.Tests.xml
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.VisualStudio.CodeCoverage.Shim.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.TestPlatform.CoreUtilities.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.TestPlatform.PlatformAbstractions.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.TestPlatform.CommunicationUtilities.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.TestPlatform.CrossPlatEngine.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.TestPlatform.Utilities.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Microsoft.VisualStudio.TestPlatform.Common.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\Newtonsoft.Json.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\NuGet.Frameworks.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.abstractions.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.assert.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.core.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\xunit.execution.dotnet.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.Shared.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.Shared.pdb
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\bin\Debug\net8.0-windows\FluentDesign.Shared.xml
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.csproj.AssemblyReference.cache
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.GeneratedMSBuildEditorConfig.editorconfig
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.AssemblyInfoInputs.cache
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.AssemblyInfo.cs
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.csproj.CoreCompileInputs.cache
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDe.F7894E24.Up2Date
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\refint\FluentDesign.WPF.Tests.dll
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.xml
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.pdb
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\FluentDesign.WPF.Tests.genruntimeconfig.cache
D:\02 FluentDesign.WPF\tests\FluentDesign.WPF.Tests\obj\Debug\net8.0-windows\ref\FluentDesign.WPF.Tests.dll
